import React, { useState, useEffect } from 'react'
import { View } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { userApi, commonApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import UserCacheManager from '@/utils/cache/userCache'
import { GoldenRescueForm } from './components'
import type { FilterOption } from './types'
import './index.scss'

const GoldenRescue: React.FC = () => {
  const router = useRouter()
  const { type } = router.params || '1'
  const isLoggedIn = UserCacheManager.isLoggedIn()
  const [pageTitle, setPageTitle] = useState('黄金救援')

  // 表单数据状态
  const [formData, setFormData] = useState<UserAPI.CreateLawEntrustOrderRequest>({
    caseTypeId: 0,
    caseStageId: 0,
    handlingAgency: '',
    province: '',
    city: '',
    district: '',
    clientName: '',
    mobile: '',
    code: '',
    source: 'rescue',
    amountInvolvedOfCase: undefined,
    lawyerRequirements: ''
  })

  const [submitting, setSubmitting] = useState(false)

  // 分类数据
  const [caseCategories, setCaseCategories] = useState<FilterOption[]>([])
  const [caseStages, setCaseStages] = useState<FilterOption[]>([])
  const [categoriesLoading, setCategoriesLoading] = useState(false)
  const [stagesLoading, setStagesLoading] = useState(false)

  // 律师列表数据（优配律师场景使用）
  // const [lawyerList, setLawyerList] = useState<FilterOption[]>([])
  // const [lawyersLoading, setLawyersLoading] = useState(false)

  // 加载案件分类列表
  const loadCaseCategories = async () => {
    try {
      setCategoriesLoading(true)

      // 先尝试从缓存获取
      const cachedCategories = await CategoryCacheManager.getCaseCategoryList()
      if (cachedCategories && cachedCategories.length > 0) {
        setCaseCategories(cachedCategories.map(item => ({
          id: item.id,
          name: item.name
        })))
        return
      }
    } catch (error) {
      console.error('加载案件分类失败:', error)
      Taro.showToast({
        title: '加载案件分类失败',
        icon: 'none'
      })
    } finally {
      setCategoriesLoading(false)
    }
  }

  // 加载案件阶段列表
  const loadCaseStages = async () => {
    try {
      setStagesLoading(true)

      const response = await commonApi.getCaseStageList()
      if (response.code === STATUS_CODE.SUCCESS && response.data?.list) {
        const stages = response.data.list.map(item => ({
          id: item.id,
          name: item.stageName
        }))
        setCaseStages(stages)
      }
    } catch (error) {
      console.error('加载案件阶段失败:', error)
      Taro.showToast({
        title: '加载案件阶段失败',
        icon: 'none'
      })
    } finally {
      setStagesLoading(false)
    }
  }

  // 加载律师列表（优配律师场景使用）
  // const loadLawyerList = async (province?: string, city?: string, district?: string) => {
  //   // 只有优配律师场景且有地区信息时才加载
  //   if (type !== '2' || !province || !city || !district) {
  //     setLawyerList([])
  //     return
  //   }

  //   try {
  //     setLawyersLoading(true)

  //     const response = await lawyerApi.getLawyerList({
  //       province,
  //       city,
  //       district,
  //       page: 1,
  //       pageSize: 100 // 获取足够多的律师供选择
  //     })

  //     if (response.code === STATUS_CODE.SUCCESS && response.data?.list) {
  //       const lawyers = response.data.list.map(item => ({
  //         id: item.userId,
  //         name: item.name
  //       }))
  //       setLawyerList(lawyers)
  //     } else {
  //       setLawyerList([])
  //     }
  //   } catch (error) {
  //     console.error('加载律师列表失败:', error)
  //     setLawyerList([])
  //     Taro.showToast({
  //       title: '加载律师列表失败',
  //       icon: 'none'
  //     })
  //   } finally {
  //     setLawyersLoading(false)
  //   }
  // }

  // 表单数据更新处理
  const handleFormDataChange = (newData: Partial<UserAPI.CreateLawEntrustOrderRequest>) => {
    setFormData(prev => ({
      ...prev,
      ...newData
    }))
  }

  // 地区变化处理（用于刷新律师列表）
  const handleAreaChange = (province: string, city: string, district: string) => {
    // 更新表单数据
    handleFormDataChange({ province, city, district })

    // 如果是优配律师场景，刷新律师列表
    // if (type === '2') {
    //   loadLawyerList(province, city, district)
    // }
  }

  // 表单验证
  const validateForm = (): boolean => {
    if (!formData.caseTypeId) {
      Taro.showToast({
        title: '请选择案件类型',
        icon: 'none'
      })
      return false
    }

    if (!formData.caseStageId) {
      Taro.showToast({
        title: '请选择案件阶段',
        icon: 'none'
      })
      return false
    }

    if (!formData.handlingAgency.trim()) {
      Taro.showToast({
        title: '请输入办案机关',
        icon: 'none'
      })
      return false
    }

    if (!formData.province || !formData.city || !formData.district) {
      Taro.showToast({
        title: '请选择地区',
        icon: 'none'
      })
      return false
    }

    if (!formData.clientName.trim()) {
      Taro.showToast({
        title: '请输入委托人姓名',
        icon: 'none'
      })
      return false
    }

    if (!formData.mobile.trim()) {
      Taro.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return false
    }

    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(formData.mobile.trim())) {
      Taro.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return false
    }

    if (!formData.code.trim()) {
      Taro.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return false
    }

    // 优配律师模式下，律师选择为必填
    // if (type === '2' && !formData.lawyerId) {
    //   Taro.showToast({
    //     title: '请选择律师',
    //     icon: 'none'
    //   })
    //   return false
    // }

    // 优配律师模式下，律师要求为必填
    if (type === '2' && !formData.lawyerRequirements?.trim()) {
      Taro.showToast({
        title: '请填写对律师的要求',
        icon: 'none'
      })
      return false
    }

    return true
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    try {
      setSubmitting(true)

      const response = await userApi.createLawEntrustOrder(formData)

      if (response.code === STATUS_CODE.SUCCESS) {
        Taro.showToast({
          title: type === '2' ? '优配申请提交成功' : '救援申请提交成功',
          icon: 'success',
          duration: 2000
        })

        setTimeout(() => {
          // 跳转到我的案件列表
          Taro.navigateTo({
            url: '/subpackages/my/my-cases/index'
          })
        }, 2000)
      } else {
        throw new Error(response.message || '提交失败')
      }
    } catch (error) {
      console.error('提交订单失败:', error)
      Taro.showToast({
        title: error.message || '提交失败，请重试',
        icon: 'none'
      })
    } finally {
      setSubmitting(false)
    }
  }

  // 初始化页面参数
  const initPageParams = () => {
    if (type === '2') {
      setPageTitle('优配律师')
      setFormData(prev => ({
        ...prev,
        source: 'optimal'
      }))
    } else {
      setPageTitle('黄金救援')
      setFormData(prev => ({
        ...prev,
        source: 'rescue'
      }))
    }
  }

  // 页面初始化
  useEffect(() => {
    initPageParams()
    loadCaseCategories()
    loadCaseStages()
  }, [])

  return (
    <PageLayout
      title={pageTitle}
      showBack
      backgroundColor='#f5f5f5'
      showFloatingMenu={false}
    >
      <PageContent>
        <View className='golden-rescue'>
          {type && <GoldenRescueForm
            formData={formData}
            caseCategories={caseCategories}
            caseStages={caseStages}
            lawyerList={[]}
            categoriesLoading={categoriesLoading}
            stagesLoading={stagesLoading}
            lawyersLoading={false}
            submitting={submitting}
            onFormDataChange={handleFormDataChange}
            onAreaChange={handleAreaChange}
            onSubmit={handleSubmit}
            pageType={type}
          />}
        </View>
      </PageContent>
    </PageLayout>
  )
}

export default GoldenRescue
