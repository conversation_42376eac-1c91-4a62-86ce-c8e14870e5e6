/**
 * 过滤器组件
 * 
 * 提供可折叠的分类选择功能
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'
import ClickMoreIcon from '@/assets/images/common-icon/click_more.png'
import './index.scss'

// 选项类型
export interface FilterOption {
  id: number
  name: string
}

// 组件属性
export interface FilterSectionProps {
  // 标题
  title: string
  // 选项列表
  options: FilterOption[]
  // 当前选中的值
  selectedValue?: number
  // 是否折叠
  collapsed: boolean
  // 折叠状态切换回调
  onToggleCollapse: () => void
  // 选项选择回调
  onSelect: (value?: number | string) => void
  // 自定义样式类名
  className?: string
}

const FilterSection: React.FC<FilterSectionProps> = ({
  title,
  options,
  selectedValue,
  collapsed,
  onToggleCollapse,
  onSelect,
  className = ''
}) => {
  // 处理选项点击
  const handleOptionClick = (value?: number) => {
    onSelect(Number(value))
  }

  // 获取当前选中的选项名称
  const getSelectedOptionName = (val: number | string | undefined): string => {
    if (!val) return '全部'
    const selectedOption = options.find(option => option.id === val)
    return selectedOption ? selectedOption.name : '全部'
  }

  return (
    <View className={`filter-section-component ${className}`}>
      {/* 过滤器头部 */}
      <View className='filter-section-component__header' onClick={onToggleCollapse}>
        <View className='filter-section-component__left'>
          <Text className='filter-section-component__title'>{title}</Text>
          <Text className='filter-section-component__selected'>{getSelectedOptionName(selectedValue)}</Text>
        </View>
        <Image
          src={ClickMoreIcon}
          className={`filter-section-component__arrow ${collapsed ? '' : 'expanded'}`}
          mode='aspectFit'
        />
      </View>

      {/* 过滤器内容 */}
      <View className={`filter-section-component__content ${collapsed ? 'collapsed' : 'expanded'}`}>
        <View className='filter-section-component__options'>
          {/* 全部选项 */}
          <View
            className={`filter-section-component__option ${!selectedValue ? 'active' : ''}`}
            onClick={() => handleOptionClick(undefined)}
          >
            <Text className='filter-section-component__option-text'>全部</Text>
          </View>

          {/* 其他选项 */}
          {options.map((option) => (
            <View
              key={option.id}
              className={`filter-section-component__option ${selectedValue === option.id ? 'active' : ''}`}
              onClick={() => handleOptionClick(option.id)}
            >
              <Text className='filter-section-component__option-text'>{option.name}</Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  )
}

export default FilterSection
