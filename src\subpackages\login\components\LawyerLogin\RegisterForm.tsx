/**
 * 注册表单组件
 */
import React, { useState, useEffect } from 'react'
import { View, Input, Button, Text, Image, CheckboxGroup, Checkbox } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { commonApi } from '@/apis/common'
import { STATUS_CODE } from '@/utils/request/config'
import AreaSelect from '@/components/AreaSelect'
import type { AreaSelectResult } from '@/components/AreaSelect/types'
import eyeIcon from '@/assets/images/common-icon/eye.png'
import eyeCloseIcon from '@/assets/images/common-icon/eye-close.png'
import './common.scss'

interface RegisterFormProps {
  onSwitchToLogin: () => void
  redirect?: string
  target?: string
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onSwitchToLogin, redirect, target }) => {
  const [formData, setFormData] = useState<CommonAPI.UserRegisterRequest>({
    userName: '',
    password: '',
    password2: '',
    phone: '',
    code: '',
    type: 2,
    province: '',
    city: '',
    district: '',
    idCard: '',
    licenseNum: '',
    lawFirm: ''
  })
  const [loading, setLoading] = useState(false)
  const [codeLoading, setCodeLoading] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [showPassword, setShowPassword] = useState(false)
  const [showPassword2, setShowPassword2] = useState(false)
  const [agreeTerms, setAgreeTerms] = useState(false)

  // 倒计时效果
  useEffect(() => {
    let timer: any
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
    }
    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [countdown])

  // 表单输入处理
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 地区选择处理
  const handleAreaChange = (result: AreaSelectResult) => {
    setFormData(prev => ({
      ...prev,
      province: result.province || '',
      city: result.city || '',
      district: result.district || ''
    }))
  }

  // 协议确认处理
  const handleAgreeTermsChange = (e: any) => {
    setAgreeTerms(e.detail.value.length > 0)
  }

  // 查看用户协议
  const handleViewUserAgreement = () => {
    // 这里可以跳转到用户协议页面或显示弹窗
    Taro.showModal({
      title: '用户协议',
      content: '这里是用户协议的内容...',
      showCancel: false,
      confirmText: '我知道了'
    })
  }

  // 查看隐私条款
  const handleViewPrivacyPolicy = () => {
    // 这里可以跳转到隐私条款页面或显示弹窗
    Taro.showModal({
      title: '隐私条款',
      content: '这里是隐私条款的内容...',
      showCancel: false,
      confirmText: '我知道了'
    })
  }

  // 手机号验证
  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  // 密码验证
  const validatePassword = (password: string): boolean => {
    return password.length >= 6 && password.length <= 20
  }

  // 发送验证码
  const handleSendCode = async () => {
    if (!validatePhone(formData.phone)) {
      Taro.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }

    setCodeLoading(true)
    try {
      const response = await commonApi.getPhoneVerificationCode({
        phone: formData.phone,
        bizCode: 'miniRegister',
        bizType: 'mini'
      })

      if (response.code === STATUS_CODE.SUCCESS) {
        Taro.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
        setCountdown(60) // 60秒倒计时
      } else {
        throw new Error(response.message || '发送失败')
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      Taro.showToast({
        title: error.message || '发送验证码失败',
        icon: 'none'
      })
    } finally {
      setCodeLoading(false)
    }
  }

  // 身份证号验证
  const validateIdCard = (idCard: string): boolean => {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  }

  // 表单验证
  const validateForm = (): boolean => {
    if (!formData.userName.trim()) {
      Taro.showToast({
        title: '请输入用户名',
        icon: 'none'
      })
      return false
    }
    if (!validatePassword(formData.password)) {
      Taro.showToast({
        title: '密码长度为6-20位',
        icon: 'none'
      })
      return false
    }
    if (formData.password !== formData.password2) {
      Taro.showToast({
        title: '两次密码输入不一致',
        icon: 'none'
      })
      return false
    }
    if (!validatePhone(formData.phone)) {
      Taro.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return false
    }
    if (!formData.code.trim()) {
      Taro.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return false
    }

    // 律师注册需要额外验证地区信息
    if (!formData.province || !formData.city || !formData.district) {
      Taro.showToast({
        title: '请选择执业地区',
        icon: 'none'
      })
      return false
    }

    // 验证身份证号（可选，但如果填写了需要格式正确）
    if (formData.idCard && !validateIdCard(formData.idCard)) {
      Taro.showToast({
        title: '请输入正确的身份证号',
        icon: 'none'
      })
      return false
    }

    // 验证律师执业证号（可选）
    if (formData.licenseNum && formData.licenseNum.trim().length < 6) {
      Taro.showToast({
        title: '律师执业证号格式不正确',
        icon: 'none'
      })
      return false
    }

    // 验证律所名称（可选）
    if (formData.lawFirm && formData.lawFirm.trim().length < 2) {
      Taro.showToast({
        title: '律所名称至少2个字符',
        icon: 'none'
      })
      return false
    }

    // 验证用户协议确认
    if (!agreeTerms) {
      Taro.showToast({
        title: '请先同意用户协议和隐私条款',
        icon: 'none'
      })
      return false
    }

    return true
  }

  // 用户注册
  const handleRegister = async () => {
    if (!validateForm()) return

    setLoading(true)
    try {
      // 构建注册请求数据
      const registerData: CommonAPI.UserRegisterRequest = {
        userName: formData.userName.trim(),
        password: formData.password.trim(),
        password2: formData.password2.trim(),
        phone: formData.phone.trim(),
        code: formData.code.trim(),
        type: 2,
        province: formData.province,
        city: formData.city,
        district: formData.district
      }

      // 添加可选字段（如果有值）
      if (formData.idCard?.trim()) {
        registerData.idCard = formData.idCard.trim()
      }
      if (formData.licenseNum?.trim()) {
        registerData.licenseNum = formData.licenseNum.trim()
      }
      if (formData.lawFirm?.trim()) {
        registerData.lawFirm = formData.lawFirm.trim()
      }

      const response = await commonApi.userRegister(registerData)

      if (response.code === STATUS_CODE.SUCCESS) {
        Taro.showToast({
          title: '注册成功',
          icon: 'success'
        })

        // 注册成功后切换到登录
        setTimeout(() => {
          onSwitchToLogin()
        }, 1500)
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      console.error('注册失败:', error)
      Taro.showToast({
        title: error.message || '注册失败，请重试',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <View className='register-form'>
      <View className='form-group'>
        <View className='input-label'>用户名</View>
        <Input
          className='form-input'
          placeholder='请输入用户名'
          value={formData.userName}
          onInput={(e) => handleInputChange('userName', e.detail.value)}
          maxlength={50}
        />
      </View>

      <View className='form-group'>
        <View className='input-label'>密码</View>
        <View className='password-input-group'>
          <Input
            className='form-input password-input'
            placeholder='请输入密码（6-20位）'
            password={!showPassword}
            value={formData.password}
            onInput={(e) => handleInputChange('password', e.detail.value)}
            maxlength={20}
          />
          <View
            className='password-toggle'
            onClick={() => setShowPassword(!showPassword)}
          >
            <Image
              className={`eye-icon ${showPassword ? 'eye-icon--active' : ''}`}
              src={showPassword ? eyeCloseIcon : eyeIcon}
              mode='aspectFit'
            />
          </View>
        </View>
      </View>

      <View className='form-group'>
        <View className='input-label'>确认密码</View>
        <View className='password-input-group'>
          <Input
            className='form-input password-input'
            placeholder='请再次输入密码'
            password={!showPassword2}
            value={formData.password2}
            onInput={(e) => handleInputChange('password2', e.detail.value)}
            maxlength={20}
          />
          <View
            className='password-toggle'
            onClick={() => setShowPassword2(!showPassword2)}
          >
            <Image
              className={`eye-icon ${showPassword2 ? 'eye-icon--active' : ''}`}
              src={showPassword2 ? eyeCloseIcon : eyeIcon}
              mode='aspectFit'
            />
          </View>
        </View>
      </View>

      <View className='form-group'>
        <View className='input-label'>手机号</View>
        <Input
          className='form-input'
          placeholder='请输入手机号'
          type='number'
          value={formData.phone}
          onInput={(e) => handleInputChange('phone', e.detail.value)}
          maxlength={11}
        />
      </View>

      <View className='form-group'>
        <View className='input-label'>验证码</View>
        <View className='code-input-group'>
          <Input
            className='form-input code-input'
            placeholder='请输入验证码'
            type='number'
            value={formData.code}
            onInput={(e) => handleInputChange('code', e.detail.value)}
            maxlength={6}
          />
          <Button
            className='code-btn'
            onClick={handleSendCode}
            loading={codeLoading}
            disabled={codeLoading || countdown > 0}
          >
            {countdown > 0 ? `${countdown}s` : '获取验证码'}
          </Button>
        </View>
      </View>

      <View className='form-group'>
        <View className='input-label'>执业地区 <Text className='required-mark'>*</Text></View>
        <AreaSelect onChange={handleAreaChange}>
          <View className='area-select-trigger'>
            <Text className={`area-select-text ${!formData.province ? 'placeholder' : ''}`}>
              {formData.province && formData.city && formData.district
                ? `${formData.province}/${formData.city}/${formData.district}`
                : '请选择执业地区'
              }
            </Text>
            <Text className='area-select-arrow'>›</Text>
          </View>
        </AreaSelect>
      </View>

      <View className='form-group'>
        <View className='input-label'>身份证号 <Text className='optional-mark'>（选填）</Text></View>
        <Input
          className='form-input'
          placeholder='请输入身份证号'
          value={formData.idCard}
          onInput={(e) => handleInputChange('idCard', e.detail.value)}
          maxlength={18}
        />
      </View>

      <View className='form-group'>
        <View className='input-label'>律师执业证号 <Text className='optional-mark'>（选填）</Text></View>
        <Input
          className='form-input'
          placeholder='请输入律师执业证号'
          value={formData.licenseNum}
          onInput={(e) => handleInputChange('licenseNum', e.detail.value)}
          maxlength={50}
        />
      </View>

      <View className='form-group'>
        <View className='input-label'>律所名称 <Text className='optional-mark'>（选填）</Text></View>
        <Input
          className='form-input'
          placeholder='请输入律所名称'
          value={formData.lawFirm}
          onInput={(e) => handleInputChange('lawFirm', e.detail.value)}
          maxlength={100}
        />
      </View>

      {/* 用户协议确认 */}
      <View className='agreement-section'>
        <CheckboxGroup onChange={handleAgreeTermsChange}>
          <View className='agreement-item'>
            <Checkbox value='agree' checked={agreeTerms} className='agreement-checkbox' />
            <View className='agreement-text'>
              <Text className='agreement-prefix'>我已阅读并同意</Text>
              <Text className='agreement-link' onClick={handleViewUserAgreement}>《用户协议》</Text>
              <Text className='agreement-separator'>和</Text>
              <Text className='agreement-link' onClick={handleViewPrivacyPolicy}>《隐私条款》</Text>
            </View>
          </View>
        </CheckboxGroup>
      </View>

      <Button
        className={`register-btn ${!agreeTerms ? 'register-btn--disabled' : ''}`}
        onClick={handleRegister}
        loading={loading}
        disabled={loading || !agreeTerms}
      >
        {loading ? '注册中...' : '注册'}
      </Button>

      <View className='login-tip'>
        <Text className='tip-text'>已有账号？</Text>
        <Text className='login-link' onClick={onSwitchToLogin}>
          立即登录
        </Text>
      </View>
    </View>
  )
}

export default RegisterForm
