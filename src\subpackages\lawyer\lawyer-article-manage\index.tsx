import React, { useState } from 'react'
import { useUpdateEffect } from 'ahooks'
import { View, Input, Image, Text, ScrollView } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { articleApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import { formatTime } from '@/utils'
import SearchIcon from '@/assets/images/common-icon/search.png'
import AddIcon from '@/assets/images/common-icon/plus.png'
import '@/styles/lawyer-manage-list.scss'

const PAGE_SIZE = 20

const LawyerArticleManage: React.FC = () => {
  const router = useRouter()
  const { lawyerId } = router.params

  // 状态管理
  const [searchKeyword, setSearchKeyword] = useState('')
  const [articleList, setArticleList] = useState<ArticleAPI.MyArticleInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [filterParams, setFilterParams] = useState<ArticleAPI.GetMyArticleListRequest>({
    page: 1,
    pageSize: PAGE_SIZE
  })


  // 加载文章列表
  const loadArticleList = async (val?: ArticleAPI.GetMyArticleListRequest) => {
    if (loading) return
    try {
      setLoading(true)
      const response = await articleApi.getMyArticleList({ ...val })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newList = response.data.list || []
        if (response.data.page === 1) {
          setArticleList(newList)
        } else {
          setArticleList(prev => [...prev, ...newList])
        }
        // 判断是否还有更多数据
        setHasMore(response.data.pageCount > response.data.page && response.data.pageCount !== 0)
      } else {
        console.log('接口无数据返回')
      }
    } catch (error) {
      console.error('加载文章列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 搜索处理
  const handleSearch = () => {
    setFilterParams(prev => ({
      ...prev,
      page: 1,
      title: searchKeyword
    }))
  }

  // 搜索输入处理
  const handleSearchInput = (e: any) => {
    setSearchKeyword(e.detail.value)
  }

  // 搜索确认处理
  const handleSearchConfirm = (e: any) => {
    const value = e.detail.value
    setSearchKeyword(value)
    setFilterParams(prev => ({
      ...prev,
      page: 1,
      title: value
    }))
  }

  // 加载更多
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = filterParams.page ? filterParams.page + 1 : 1
      setFilterParams(prev => ({
        ...prev,
        page: nextPage
      }))
    }
  }

  // 跳转到新建文章页面
  const handleAddArticle = () => {
    Taro.navigateTo({
      url: '/subpackages/publish/publish-article/index'
    })
  }

  // 跳转到编辑文章页面
  const handleArticleClick = (article: ArticleAPI.MyArticleInfo) => {
    Taro.navigateTo({
      url: `/subpackages/publish/publish-article/index?id=${article.id}`
    })
  }

  // 下拉刷新
  const handleRefresh = async () => {
    const newFilters = {
      ...filterParams,
      page: 1
    }
    setFilterParams(newFilters)
    setHasMore(true)
  }



  // 获取审核状态信息
  const getStatusInfo = (status: number) => {
    switch (status) {
      case 1:
        return { text: '待审核', className: 'status-pending' }
      case 2:
        return { text: '审核通过', className: 'status-approved' }
      case 3:
        return { text: '审核不通过', className: 'status-rejected' }
      default:
        return { text: '未知状态', className: 'status-unknown' }
    }
  }

  // 页面初始化
  Taro.useDidShow(() => {
    if (lawyerId) {
      loadArticleList(filterParams)
    }
  })

  useUpdateEffect(() => {
    loadArticleList(filterParams)
  }, [filterParams])

  return (
    <PageLayout
      title='文章管理'
      showBack
      scrollable={false}
      showFloatingMenu={false}
    >
      <PageContent>
        <View className='lawyer-content-manage'>
          {/* 搜索栏 */}
          <View className='lawyer-search-header'>
            <View className='lawyer-search-box'>
              <Image
                src={SearchIcon}
                className='lawyer-search-icon'
                mode='aspectFit'
                onClick={handleSearch}
              />
              <Input
                className='lawyer-search-input'
                placeholder='搜索文章标题'
                value={searchKeyword}
                onInput={handleSearchInput}
                onConfirm={handleSearchConfirm}
                confirmType='search'
              />
            </View>
            <View className='lawyer-add-button' onClick={handleAddArticle}>
              <Image src={AddIcon} className='lawyer-add-icon' mode='aspectFit' />
            </View>
          </View>

          {/* 文章列表 */}
          <View className='lawyer-content-list-container'>
            <ScrollView
              className='lawyer-content-list'
              scrollY
              onScrollToLower={handleLoadMore}
              lowerThreshold={100}
              refresherEnabled
              refresherTriggered={loading && filterParams.page === 1}
              onRefresherRefresh={handleRefresh}
            >
              {articleList.map((article) => {
                const statusInfo = getStatusInfo(article.status)
                return (
                  <View
                    key={article.id}
                    className='lawyer-content-card'
                    onClick={() => handleArticleClick(article)}
                  >
                    <View className='lawyer-content-header'>
                      <View className='lawyer-content-title-row'>
                        <Text className='lawyer-content-title'>{article.title}</Text>
                        <View className={`lawyer-content-status ${statusInfo.className}`}>
                          <Text className='status-text'>{statusInfo.text}</Text>
                        </View>
                      </View>
                      {article.status === 3 && article.rejectReason && (
                        <View className='reject-reason'>
                          <Text className='reject-reason-text'>驳回原因：{article.rejectReason}</Text>
                        </View>
                      )}
                    </View>
                    <View className='lawyer-content-footer'>
                      <View className='lawyer-content-left'>
                        <Text className='lawyer-content-stats'>浏览 {article.viewCount}</Text>
                        <Text className='lawyer-content-stats'>点赞 {article.likeCount}</Text>
                        <Text className='lawyer-content-stats'>收藏 {article.favoriteCount}</Text>
                      </View>
                      <Text className='lawyer-content-time'>{formatTime(article.createdAt!)}</Text>
                    </View>
                  </View>
                )
              })}

              {/* 加载状态 */}
              {loading && (
                <View className='lawyer-loading-more'>
                  <Text>加载中...</Text>
                </View>
              )}

              {/* 没有更多数据 */}
              {!hasMore && articleList.length > 0 && (
                <View className='lawyer-no-more'>
                  <Text>没有更多文章了</Text>
                </View>
              )}

              {/* 空状态 */}
              {!loading && articleList.length === 0 && (
                <View className='lawyer-empty-state'>
                  <Text>暂无文章</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </PageContent>
    </PageLayout>
  )
}

export default LawyerArticleManage
