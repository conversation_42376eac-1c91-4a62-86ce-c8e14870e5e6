/**
 * 登录页面
 * 支持委托人和律师两种角色登录
 */
import React, { useState } from 'react'
import { View, Image } from '@tarojs/components'
import PageLayout, { PageContent } from '@/components/PageLayout'
import clientIcon from '@/assets/images/common-icon/client.png'
import scalesIcon from '@/assets/images/common-icon/scales.png'
import Taro, { useRouter } from '@tarojs/taro'
import { isTabBarPage } from '@/utils/helpers/navigation'
import ClientLogin from './components/ClientLogin'
import LawyerLogin from './components/LawyerLogin'

import './index.scss'

// 用户角色类型
type UserRole = 'client' | 'lawyer'

const Login: React.FC = () => {
  const router = useRouter()
  const { redirect, target } = router.params
  // 当前选择的角色
  const [currentRole, setCurrentRole] = useState<UserRole>('client')

  // 角色切换
  const handleRoleSwitch = (role: UserRole) => {
    setCurrentRole(role)
  }

  // 处理登录成功后的跳转
  const handleLoginSuccess = () => {
    if (redirect === 'true' && target) {
      // 有重定向目标，跳转到目标页面
      if (isTabBarPage(target)) {
        Taro.switchTab({ url: target })
      } else {
        Taro.navigateBack()
      }
    } else {
      // 默认跳转到我的页面
      Taro.switchTab({ url: '/pages/mine/index' })
    }
  }

  return (
    <View className='login-page'>
      <PageLayout
        title='登录'
        showBack
        backgroundColor='#ffffff'
        showFloatingMenu={false}
      >
        <PageContent>
          <View className='login-content'>
            {/* 页面标题 */}
            <View className='page-header'>
              <View className='header-title'>欢迎使用</View>
              <View className='header-subtitle'>胜张仪法律服务平台</View>
            </View>

            {/* 角色选择卡片 */}
            <View className='role-selector'>
              <View
                className={`role-card ${currentRole === 'client' ? 'role-card--active' : ''}`}
                onClick={() => handleRoleSwitch('client')}
              >
                <View className='role-icon'>
                  <Image src={clientIcon} mode='aspectFit' className='role-icon-img' />
                </View>
                <View className='role-title'>用户</View>
                <View className='role-desc'>手机号快速登录</View>
              </View>
              <View
                className={`role-card ${currentRole === 'lawyer' ? 'role-card--active' : ''}`}
                onClick={() => handleRoleSwitch('lawyer')}
              >
                <View className='role-icon'>
                  <Image src={scalesIcon} mode='aspectFit' className='role-icon-img' />
                </View>
                <View className='role-title'>律师</View>
                <View className='role-desc'>专业律师入口</View>
              </View>
            </View>

            {/* 登录内容区域 */}
            <View className='login-container'>
              {currentRole === 'client' ? (
                <ClientLogin onLoginSuccess={handleLoginSuccess} />
              ) : (
                <LawyerLogin onLoginSuccess={handleLoginSuccess} />
              )}
            </View>
          </View>
        </PageContent>
      </PageLayout>
    </View>
  )
}

export default Login
