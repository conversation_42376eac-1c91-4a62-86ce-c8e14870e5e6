.filter-section-component {
  flex-shrink: 0;
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;
  }

  &__left {
    display: flex;
    align-items: center;
    gap: 15rpx;
    flex: 1;
  }

  &__title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    flex-shrink: 0;
  }

  &__selected {
    font-size: 26rpx;
    color: #BD8A4F;
    background: rgba(189, 138, 79, 0.1);
    padding: 4rpx 12rpx;
    border-radius: 16rpx;
    white-space: nowrap;
    max-width: 200rpx;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__arrow {
    width: 24rpx;
    height: 24rpx;
    transition: transform 0.3s ease;
    flex-shrink: 0;

    &.expanded {
      transform: rotate(180deg);
    }
  }



  &__content {
    overflow: hidden;
    transition: max-height 0.3s ease;

    &.collapsed {
      max-height: 0;
    }

    &.expanded {
      max-height: 800rpx;
    }
  }



  &__options {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx 30rpx 30rpx;
    gap: 20rpx;
  }

  &__option {
    padding: 10rpx 30rpx;
    background: #f7f7f7;
    border-radius: 40rpx;

    &.active {
      background: #BD8A4F;

      .filter-section-component__option-text {
        color: #fff;
      }
    }
  }

  &__option-text {
    font-size: 28rpx;
    color: #666;
    white-space: nowrap;
  }
}