/**
 * 我的关注页面
 *
 * 展示用户关注的律师列表
 */
import React, { useState, useEffect } from 'react'
import { View, Text, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { lawyerA<PERSON> } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import FollowCard from './components/FollowCard'
import './index.scss'

const MyFollow: React.FC = () => {
  // 状态管理
  const [followList, setFollowList] = useState<LawyerAPI.OwnerLawyerFollowListInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  const pageSize = 20

  // 加载关注列表
  const loadFollowList = async (pageNum: number) => {
    if (loading) return

    try {
      setLoading(true)
      const params: LawyerAPI.GetLawyerFollowListRequest = {
        page: pageNum,
        pageSize
      }

      const response = await lawyerApi.getLawyerFollowList(params)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newList = response.data.list || []

        if (pageNum === 1) {
          setFollowList(newList)
        } else {
          setFollowList(prev => [...prev, ...newList])
        }

        // 判断是否还有更多数据
        setHasMore(response.data.pageCount > response.data.page && response.data.pageCount !== 0)
        setPage(pageNum)
      } else {
        console.log('获取关注列表失败')
      }
    } catch (error) {
      console.error('加载关注列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 处理律师点击
  const handleLawyerClick = (followInfo: LawyerAPI.OwnerLawyerFollowListInfo) => {
    Taro.navigateTo({
      url: `/subpackages/detail/lawyer/index?lawyerId=${followInfo.lawyerId}`
    })
  }

  // 下拉刷新
  const handleRefresh = () => {
    loadFollowList(1)
  }

  // 加载更多
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1
      loadFollowList(nextPage)
    }
  }

  // 页面初始化
  useEffect(() =>{
    loadFollowList(1)
  }, [])

  return (
    <PageLayout
      title='我的关注'
      showBack
      scrollable={false}
    >
      <PageContent>
        <View className='my-follow'>
          <ScrollView
            className='my-follow-list'
            scrollY
            refresherEnabled
            refresherTriggered={loading && page === 1}
            onRefresherRefresh={handleRefresh}
            onScrollToLower={handleLoadMore}
          >
            {followList.length > 0 ? (
              <>
                {followList.map((followInfo) => (
                  <FollowCard
                    key={followInfo.id}
                    followInfo={followInfo}
                    onClick={handleLawyerClick}
                  />
                ))}

                {/* 加载状态 */}
                {loading && page > 1 && (
                  <View className='my-follow-loading-more'>
                    <Text>加载中...</Text>
                  </View>
                )}

                {/* 没有更多数据 */}
                {!hasMore && followList.length > 0 && (
                  <View className='my-follow-no-more'>
                    <Text>没有更多数据了</Text>
                  </View>
                )}
              </>
            ) : (
              !loading && (
                <View className='my-follow-empty-state'>
                  <Text>暂无关注的律师</Text>
                </View>
              )
            )}
          </ScrollView>
        </View>
      </PageContent>
    </PageLayout>
  )
}

export default MyFollow
