/**
 * 动态列表组件
 */
import React, { useState, useEffect, useRef } from 'react'
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { dynamicsApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import clockIcon from '@/assets/images/common-icon/clock.png'
import eyeIcon from '@/assets/images/common-icon/eye.png'

// 组件属性
export interface DynamicsListProps {
  // 律师ID
  lawyerId: number
  // 筛选条件
  filterParams?: DynamicsAPI.GetDynamicsListRequest
  // 动态点击回调
  onDynamicsClick: (dynamicsInfo: DynamicsAPI.DynamicsListInfo) => void
  // 自定义样式类名
  className?: string
}

const PAGE_SIZE = 20
const DynamicsList: React.FC<DynamicsListProps> = ({
  lawyerId,
  filterParams,
  onDynamicsClick,
  className = ''
}) => {
  // 动态列表数据
  const [dynamics, setDynamics] = useState<DynamicsAPI.DynamicsListInfo[]>([])
  // 加载状态
  const [loading, setLoading] = useState(false)
  // 当前页码
  const [page, setPage] = useState(1)
  // 是否还有更多数据
  const [hasMore, setHasMore] = useState(true)
  // 滚动加载防抖
  const scrollLoadingRef = useRef(false)

  // 加载动态列表数据
  const loadDynamics = async (pageNum: number = 1) => {
    if (loading) return
    try {
      setLoading(true)

      const requestParams = {
        page: pageNum,
        pageSize: PAGE_SIZE,
        lawyerId,
        ...filterParams
      }

      const response = await dynamicsApi.getDynamicsList(requestParams)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newDynamics = response.data.list || []

        if (pageNum === 1) {
          setDynamics(newDynamics)
        } else {
          setDynamics(prev => [...prev, ...newDynamics])
        }
        // 判断是否还有更多数据
        setHasMore(response.data.pageCount > pageNum && response.data.pageCount !== 0)
        setPage(pageNum)
      }
    } catch (error) {
      console.error('加载动态列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 处理滚动到底部加载更多
  const handleScrollToLower = async () => {
    if (!hasMore || loading || scrollLoadingRef.current) {
      return
    }

    try {
      scrollLoadingRef.current = true
      await loadDynamics(page + 1)
    } catch (error) {
      console.error('滚动加载失败:', error)
    } finally {
      scrollLoadingRef.current = false
    }
  }

  // 监听筛选条件变化，重新加载数据
  useEffect(() => {
    setPage(1)
    loadDynamics(1)
  }, [filterParams])

  // 显示加载状态
  const showLoading = loading && dynamics.length === 0

  return (
    <View className={`dynamics-list ${className}`}>
      {showLoading ? (
        <View className='dynamics-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <ScrollView
          className='dynamics-list__scroll'
          scrollY
          onScrollToLower={handleScrollToLower}
          lowerThreshold={100}
        >
          {dynamics.map((dynamicsInfo) => (
            <View
              key={dynamicsInfo.id}
              className='dynamics-item'
              onClick={() => onDynamicsClick(dynamicsInfo)}
            >
              <Text className='dynamics-item__title'>{dynamicsInfo.title}</Text>
              <View className='dynamics-item__meta'>
                <Text className='dynamics-item__category'>{dynamicsInfo.categoryName}</Text>
                <Image className='dynamics-item__info-icon' src={clockIcon} mode='aspectFit' />
                <Text className='dynamics-item__info-text'>{dynamicsInfo.createdAt}</Text>
                <Image className='dynamics-item__info-icon' src={eyeIcon} mode='aspectFit' />
                <Text className='dynamics-item__info-text'>{dynamicsInfo.viewCount}</Text>
              </View>
            </View>
          ))}

          {/* 滚动加载状态 */}
          {loading && (
            <View className='dynamics-list__scroll-loading'>
              <Text>加载中...</Text>
            </View>
          )}

          {/* 没有更多数据 */}
          {!hasMore && dynamics.length > 0 && (
            <View className='dynamics-list__no-more'>
              <Text>没有更多动态了</Text>
            </View>
          )}

          {/* 空状态 */}
          {!loading && dynamics.length === 0 && (
            <View className='dynamics-list__empty'>
              <Text>暂无动态</Text>
            </View>
          )}
        </ScrollView>
      )}
    </View>
  )
}

export default DynamicsList
