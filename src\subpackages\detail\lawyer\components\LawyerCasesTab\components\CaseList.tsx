/**
 * 案例列表组件
 */
import React, { useState, useEffect, useRef } from 'react'
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { caseApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import clockIcon from '@/assets/images/common-icon/clock.png'
import eyeIcon from '@/assets/images/common-icon/eye.png'

// 组件属性
export interface CaseListProps {
  // 律师ID
  lawyerId: number
  // 筛选条件
  filterParams?: CaseAPI.GetCaseListRequest
  // 案例点击回调
  onCaseClick: (caseInfo: CaseAPI.CaseListInfo) => void
  // 自定义样式类名
  className?: string
}
const PAGE_SIZE = 20
const CaseList: React.FC<CaseListProps> = ({
  lawyerId,
  filterParams,
  onCaseClick,
  className = ''
}) => {
  // 案例列表数据
  const [cases, setCases] = useState<CaseAPI.CaseListInfo[]>([])
  // 加载状态
  const [loading, setLoading] = useState(false)
  // 当前页码
  const [page, setPage] = useState(1)
  // 是否还有更多数据
  const [hasMore, setHasMore] = useState(true)
  // 滚动加载防抖
  const scrollLoadingRef = useRef(false)

  // 加载案例列表数据
  const loadCases = async (pageNum: number = 1) => {
    if (loading) return
    try {
      setLoading(true)

      const requestParams = {
        page: pageNum,
        pageSize: PAGE_SIZE,
        lawyerId,
        ...filterParams
      }

      const response = await caseApi.getCaseList(requestParams)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newCases = response.data.list || []

        if (pageNum === 1) {
          setCases(newCases)
        } else {
          setCases(prev => [...prev, ...newCases])
        }
        // 判断是否还有更多数据
        setHasMore(response.data.pageCount > pageNum && response.data.pageCount !== 0)
        setPage(pageNum)
      }
    } catch (error) {
      console.error('加载案例列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 处理滚动到底部加载更多
  const handleScrollToLower = async () => {
    if (!hasMore || loading || scrollLoadingRef.current) {
      return
    }

    try {
      scrollLoadingRef.current = true
      await loadCases(page + 1)
    } catch (error) {
      console.error('滚动加载失败:', error)
    } finally {
      scrollLoadingRef.current = false
    }
  }

  // 监听筛选条件变化，重新加载数据
  useEffect(() => {
    setPage(1)
    loadCases(1)
  }, [filterParams])

  // 显示加载状态
  const showLoading = loading && cases.length === 0

  return (
    <View className={`lawyer-case-detail-list ${className}`}>
      {showLoading ? (
        <View className='lawyer-case-detail-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <ScrollView
          className='lawyer-case-detail-list__scroll'
          scrollY
          onScrollToLower={handleScrollToLower}
          lowerThreshold={100}
        >
          {cases.map((caseInfo) => (
            <View
              key={caseInfo.id}
              className='lawyer-case-detail-item'
              onClick={() => onCaseClick(caseInfo)}
            >
              <Text className='lawyer-case-detail-item__title'>{caseInfo.title}</Text>
              <View className='lawyer-case-detail-item__meta'>
                <Text className='lawyer-case-detail-item__category'>{caseInfo.categoryName}</Text>
                <Image className='lawyer-case-detail-item__info-icon' src={clockIcon} mode='aspectFit' />
                <Text className='lawyer-case-detail-item__info-text'>{caseInfo.createdAt}</Text>
                <Image className='lawyer-case-detail-item__info-icon' src={eyeIcon} mode='aspectFit' />
                <Text className='lawyer-case-detail-item__info-text'>{caseInfo.viewCount}</Text>
              </View>
            </View>
          ))}

          {/* 滚动加载状态 */}
          {loading && (
            <View className='lawyer-case-detail-list__scroll-loading'>
              <Text>加载中...</Text>
            </View>
          )}

          {/* 没有更多数据 */}
          {!hasMore && cases.length > 0 && (
            <View className='lawyer-case-detail-list__no-more'>
              <Text>没有更多案例了</Text>
            </View>
          )}

          {/* 空状态 */}
          {!loading && cases.length === 0 && (
            <View className='lawyer-case-detail-list__empty'>
              <Text>暂无案例</Text>
            </View>
          )}
        </ScrollView>
      )}
    </View>
  )
}

export default CaseList
