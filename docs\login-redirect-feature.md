# 登录重定向功能说明

## 功能概述

新增了 `navigateToLogin` 方法，在用户未登录时可以跳转至登录注册页，并在登录成功后返回到原来浏览的页面。

## 主要改动

### 1. 登录页面支持 redirect 和 target 参数

登录页面 (`src/subpackages/login/index.tsx`) 现在支持以下 URL 参数：
- `redirect`: 是否为重定向登录 (值为 'true')
- `target`: 登录成功后要跳转的目标页面

### 2. ClientLogin 和 LawyerLogin 组件增加回调处理

两个登录组件都新增了 `handleLoginSuccess` 方法来统一处理登录成功后的跳转逻辑：

```typescript
// 处理登录成功后的跳转
const handleLoginSuccess = () => {
  if (redirect === 'true' && target) {
    // 有重定向目标，跳转到目标页面
    if (isTabBarPage(target)) {
      Taro.switchTab({ url: target })
    } else {
      Taro.navigateTo({ url: target })
    }
  } else {
    // 默认跳转到我的页面
    Taro.switchTab({ url: '/pages/mine/index' })
  }
}
```

### 3. navigateToLogin 方法

在 `src/utils/helpers/navigation.ts` 中已有 `navigateToLogin` 方法：

```typescript
/**
 * 未登录提示，并跳转登录页，区分tabbar页面
 */
export const navigateToLogin = async (url: string): Promise<void> => {
  if (isTabBarPage(url)) {
    await redirectTo('/subpackages/login/index?redirect=true&target=' + url)
  } else {
    await navigateToPage('/subpackages/login/index?redirect=true&target=' + url)
  }
}
```

## 使用方法

### 在需要登录验证的地方调用

```typescript
import { navigateToLogin } from '@/utils/helpers/navigation'

// 检查登录状态，未登录时跳转
if (!isLoggedIn) {
  // 传入当前页面路径，登录成功后会返回此页面
  navigateToLogin('/subpackages/detail/lawyer/index?lawyerId=123')
  return
}
```

### 已更新的使用场景

1. **我的页面** (`src/pages/mine/index.tsx`)
   - 头像点击未登录时
   - 菜单功能需要登录时

2. **律师详情页** (`src/subpackages/detail/lawyer/index.tsx`)
   - 关注操作未登录时 (401 错误)

## 工作流程

1. 用户在某个页面尝试执行需要登录的操作
2. 系统检测到用户未登录，调用 `navigateToLogin(currentPageUrl)`
3. 跳转到登录页面，URL 包含 `redirect=true&target=currentPageUrl`
4. 用户完成登录（委托人或律师登录）
5. 登录成功后，系统检查是否有重定向参数
6. 如果有，跳转到目标页面；如果没有，跳转到默认的我的页面

## 注意事项

1. **TabBar 页面处理**: 对于 TabBar 页面，使用 `Taro.switchTab` 而不是 `Taro.navigateTo`
2. **参数传递**: 目标页面的完整 URL（包括查询参数）会被正确传递和恢复
3. **注册流程**: 注册成功后会切换到登录模式，不会直接跳转到目标页面
4. **错误处理**: 登录失败时不会执行重定向，用户可以重新尝试登录

## 测试场景

1. 在未登录状态下点击我的页面头像
2. 在未登录状态下点击需要登录的菜单项
3. 在律师详情页未登录状态下点击关注按钮
4. 测试 TabBar 页面和普通页面的重定向
5. 测试带参数页面的重定向
