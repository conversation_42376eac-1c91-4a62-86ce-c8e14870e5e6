import React, { useState } from 'react'
import { View, Input, Button, Text, Image, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import AreaSelect from '@/components/AreaSelect'
import { lawyerApi } from '@/apis'
import { uploadApi } from '@/apis/upload'
import { STATUS_CODE } from '@/utils/request/config'
import { IdAvatarPlaceholder, IdBadgePlaceholder } from '@/constant/image'
import { removeImagePrefix, navigateToLogin } from '@/utils'
import UserCacheManager from '@/utils/cache/userCache'
import './index.scss'

const LawyerEntry: React.FC = () => {
  const isLoggedIn = UserCacheManager.isLoggedIn()
  // 表单数据
  const [formData, setFormData] = useState<LawyerAPI.SubmitAuthenticationRequest>({
    province: '',
    city: '',
    district: '',
    phone: '',
    lawFirm: '',
    lawFirmAddress: '',
    name: '',
    idCard: '',
    idCardFrontUrl: '',
    idCardBackUrl: '',
    isGoldenRescue: 0,
    licenseUrl: ''
  })

  // 图片上传状态
  const [imageUploads, setImageUploads] = useState({
    idCardFront: '', // 身份证正面
    idCardBack: '',  // 身份证反面
    lawyerCertificate: '' // 律师执业证
  })

  // 上传状态
  const [uploadingStates, setUploadingStates] = useState({
    idCardFront: false,
    idCardBack: false,
    lawyerCertificate: false
  })

  // 提交状态
  const [submitting, setSubmitting] = useState(false)

  // 表单输入处理
  const handleInputChange = (field: keyof LawyerAPI.SubmitAuthenticationRequest, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 地址选择处理
  const handleAreaChange = (result: any) => {
    setFormData(prev => ({
      ...prev,
      province: result.province || '',
      city: result.city || '',
      district: result.district || ''
    }))
  }

  // 身份证号验证
  const validateIdCard = (idCard: string): boolean => {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  }

  // 表单验证
  const validateForm = (): boolean => {
    if (!isLoggedIn) {
      Taro.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        navigateToLogin('/subpackages/lawyer/lawyer-entry/index')
      }, 1500)
      return false
    }
    if (!formData.name.trim()) {
      Taro.showToast({
        title: '请输入真实姓名',
        icon: 'none'
      })
      return false
    }

    if (!validateIdCard(formData.idCard)) {
      Taro.showToast({
        title: '请输入正确的身份证号',
        icon: 'none'
      })
      return false
    }

    if (!formData.phone.trim()) {
      Taro.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return false
    }

    if (!formData.lawFirm.trim()) {
      Taro.showToast({
        title: '请输入律师事务所名称',
        icon: 'none'
      })
      return false
    }

    if (!formData.lawFirmAddress.trim()) {
      Taro.showToast({
        title: '请输入律师事务所地址',
        icon: 'none'
      })
      return false
    }

    if (!formData.province || !formData.city) {
      Taro.showToast({
        title: '请选择所在地区',
        icon: 'none'
      })
      return false
    }

    if (!imageUploads.idCardFront) {
      Taro.showToast({
        title: '请上传身份证正面照片',
        icon: 'none'
      })
      return false
    }

    if (!imageUploads.idCardBack) {
      Taro.showToast({
        title: '请上传身份证反面照片',
        icon: 'none'
      })
      return false
    }

    if (!imageUploads.lawyerCertificate) {
      Taro.showToast({
        title: '请上传律师执业证照片',
        icon: 'none'
      })
      return false
    }

    return true
  }

  // 选择图片
  const chooseImage = async (type: 'idCardFront' | 'idCardBack' | 'lawyerCertificate') => {
    try {
      const res = await Taro.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      })

      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        await uploadImage(type, res.tempFilePaths[0])
      }
    } catch (error) {
      console.error('选择图片失败:', error)
      Taro.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  }

  // 上传图片
  const uploadImage = async (type: 'idCardFront' | 'idCardBack' | 'lawyerCertificate', filePath: string) => {
    try {
      setUploadingStates(prev => ({
        ...prev,
        [type]: true
      }))

      // 使用优化后的uploadApi.uploadImage方法上传
      const response = await uploadApi.uploadImage(filePath)

      if (response.code === STATUS_CODE.SUCCESS && response.data?.url) {
        const imageUrl = response.data.url

        // 更新图片上传状态
        setImageUploads(prev => ({
          ...prev,
          [type]: imageUrl
        }))

        // 根据类型更新对应的表单数据
        const fieldMap = {
          lawyerCertificate: 'licenseUrl',
          idCardFront: 'idCardFrontUrl',
          idCardBack: 'idCardBackUrl'
        } as const

        setFormData(prev => ({
          ...prev,
          [fieldMap[type]]: imageUrl
        }))

        Taro.showToast({
          title: '上传成功',
          icon: 'success'
        })
      } else {
        throw new Error(response.message || '上传失败')
      }
    } catch (error: any) {
      console.error('上传图片失败:', error)
      Taro.showToast({
        title: error.message || '上传失败',
        icon: 'none'
      })
    } finally {
      setUploadingStates(prev => ({
        ...prev,
        [type]: false
      }))
    }
  }

  // 提交认证
  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    try {
      setSubmitting(true)

      // 构建提交数据
      const submitData: LawyerAPI.SubmitAuthenticationRequest = {
        ...formData,
        idCardFrontUrl: removeImagePrefix(imageUploads.idCardFront),
        idCardBackUrl: removeImagePrefix(imageUploads.idCardBack),
        licenseUrl: removeImagePrefix(imageUploads.lawyerCertificate)
      }

      const response = await lawyerApi.submitAuthentication(submitData)

      if (response.code === STATUS_CODE.SUCCESS) {
        Taro.showToast({
          title: '提交成功，请等待审核',
          icon: 'success',
          duration: 2000
        })

        // 延迟返回上一页
        setTimeout(() => {
          Taro.navigateBack()
        }, 2000)
      } else {
        throw new Error(response.message || '提交失败')
      }
    } catch (error) {
      console.error('提交认证失败:', error)
      Taro.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <PageLayout
      title='律师入驻认证'
      showBack
      showFloatingMenu={false}
    >
      <PageContent>
        <ScrollView className='auth-form-container' scrollY>
          <View className='auth-form'>
            {/* 基本信息 */}
            <View className='form-section'>
              <Text className='section-title'>基本信息</Text>

              <View className='form-item'>
                <Text className='form-label'>
                  <Text className='required'>*</Text>真实姓名
                </Text>
                <Input
                  className='form-input'
                  placeholder='请输入真实姓名'
                  value={formData.name}
                  onInput={(e) => handleInputChange('name', e.detail.value)}
                />
              </View>

              <View className='form-item'>
                <Text className='form-label'>
                  <Text className='required'>*</Text>身份证号
                </Text>
                <Input
                  className='form-input'
                  placeholder='请输入身份证号'
                  value={formData.idCard}
                  onInput={(e) => handleInputChange('idCard', e.detail.value)}
                />
              </View>

              <View className='form-item'>
                <Text className='form-label'>
                  <Text className='required'>*</Text>手机号
                </Text>
                <Input
                  className='form-input'
                  placeholder='请输入手机号'
                  value={formData.phone}
                  onInput={(e) => handleInputChange('phone', e.detail.value)}
                />
              </View>
            </View>

            {/* 执业信息 */}
            <View className='form-section'>
              <Text className='section-title'>执业信息</Text>

              <View className='form-item'>
                <Text className='form-label'>
                  <Text className='required'>*</Text>律师事务所
                </Text>
                <Input
                  className='form-input'
                  placeholder='请输入律师事务所名称'
                  value={formData.lawFirm}
                  onInput={(e) => handleInputChange('lawFirm', e.detail.value)}
                />
              </View>

              <View className='form-item'>
                <Text className='form-label'>
                  <Text className='required'>*</Text>律师事务所地址
                </Text>
                <Input
                  className='form-input'
                  placeholder='请输入律师事务所地址'
                  value={formData.lawFirmAddress}
                  onInput={(e) => handleInputChange('lawFirmAddress', e.detail.value)}
                />
              </View>

              <View className='form-item'>
                <Text className='form-label'>
                  <Text className='required'>*</Text>所在地区
                </Text>
                <AreaSelect onChange={handleAreaChange}>
                  <View className='area-select-trigger'>
                    <Text className={`area-select-text ${!formData.province ? 'placeholder' : ''}`}>
                      {formData.province && formData.city
                        ? `${formData.province}/${formData.city}${formData.district ? '/' + formData.district : ''}`
                        : '请选择省市区'
                      }
                    </Text>
                    <View className='area-select-arrow' />
                  </View>
                </AreaSelect>
              </View>

              <View className='form-item'>
                <Text className='form-label'>黄金救援</Text>
                <View className='checkbox-container'>
                  <View
                    className={`checkbox ${formData.isGoldenRescue === 1 ? 'checked' : ''}`}
                    onClick={() => setFormData(prev => ({ ...prev, isGoldenRescue: prev.isGoldenRescue === 1 ? 0 : 1 }))}
                  >
                    {formData.isGoldenRescue === 1 && <Text className='checkbox-icon'>✓</Text>}
                  </View>
                  <Text className='checkbox-label'>参与黄金救援服务</Text>
                </View>
              </View>
            </View>

            {/* 证件上传 */}
            <View className='form-section'>
              <Text className='section-title'>证件上传</Text>

              {/* 身份证正面 */}
              <View className='form-item'>
                <Text className='form-label'>
                  <Text className='required'>*</Text>身份证人像面
                </Text>
                <View className='upload-container'>
                  <View
                    className='upload-item'
                    onClick={() => chooseImage('idCardFront')}
                  >
                    {imageUploads.idCardFront ? (
                      <Image
                        src={imageUploads.idCardFront}
                        className='upload-preview'
                        mode='aspectFill'
                      />
                    ) : (
                      <View className='upload-placeholder'>
                        <Image
                          src={IdAvatarPlaceholder}
                          className='placeholder-icon'
                          mode='aspectFit'
                        />
                      </View>
                    )}
                    {uploadingStates.idCardFront && (
                      <View className='upload-loading'>
                        <Text>上传中...</Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>

              {/* 身份证反面 */}
              <View className='form-item'>
                <Text className='form-label'>
                  <Text className='required'>*</Text>身份证国徽面
                </Text>
                <View className='upload-container'>
                  <View
                    className='upload-item'
                    onClick={() => chooseImage('idCardBack')}
                  >
                    {imageUploads.idCardBack ? (
                      <Image
                        src={imageUploads.idCardBack}
                        className='upload-preview'
                        mode='aspectFill'
                      />
                    ) : (
                      <View className='upload-placeholder'>
                        <Image
                          src={IdBadgePlaceholder}
                          className='placeholder-icon'
                          mode='aspectFit'
                        />
                      </View>
                    )}
                    {uploadingStates.idCardBack && (
                      <View className='upload-loading'>
                        <Text>上传中...</Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>

              {/* 律师执业证 */}
              <View className='form-item'>
                <Text className='form-label'>
                  <Text className='required'>*</Text>律师执业证
                </Text>
                {imageUploads.lawyerCertificate ? (
                  <View className='upload-container'
                    onClick={() => chooseImage('lawyerCertificate')}
                  >
                    <View className='upload-item'>
                      <Image
                        src={imageUploads.lawyerCertificate}
                        className='upload-preview'
                        mode='aspectFill'
                      />
                    </View>
                  </View>
                ) : (
                  <View className='certificate-upload-section'>
                    <Button
                      className='certificate-upload-button'
                      onClick={() => chooseImage('lawyerCertificate')}
                      loading={uploadingStates.lawyerCertificate}
                      disabled={uploadingStates.lawyerCertificate}
                    >
                      {uploadingStates.lawyerCertificate ? '上传中...' : '选择文件'}
                    </Button>
                    <Text className='certificate-upload-text'>请上传律师执业证照片</Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        </ScrollView>

        {/* 固定在底部的提交按钮 */}
        <View className='submit-container'>
          <Button
            className='submit-button'
            onClick={handleSubmit}
            loading={submitting}
            disabled={submitting}
          >
            {submitting ? '提交中...' : '提交认证'}
          </Button>
        </View>
      </PageContent>
    </PageLayout>
  )
}

export default LawyerEntry
