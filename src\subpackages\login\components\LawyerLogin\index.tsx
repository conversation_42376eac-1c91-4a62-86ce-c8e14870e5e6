/**
 * 律师登录组件
 * 支持账号登录和手机验证码登录，包含注册功能
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Input, Button, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { commonApi } from '@/apis/common'
import { STATUS_CODE } from '@/utils/request/config'
import eyeIcon from '@/assets/images/common-icon/eye.png'
import eyeCloseIcon from '@/assets/images/common-icon/eye-close.png'
import { aesEcb } from '@/utils'
import { isTabBarPage } from '@/utils/helpers/navigation'
import RegisterForm from './RegisterForm'
import './index.scss'

// 登录方式类型
type LoginMode = 'account' | 'phone' | 'register'

interface LawyerLoginProps {
  redirect?: string
  target?: string
}

const LawyerLogin: React.FC<LawyerLoginProps> = ({ redirect, target }) => {
  // 当前登录方式
  const [currentMode, setCurrentMode] = useState<LoginMode>('account')

  // 表单数据
  const [formData, setFormData] = useState({
    userName: '',
    password: '',
    phone: '',
    code: ''
  })

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [codeLoading, setCodeLoading] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [showPassword, setShowPassword] = useState(false)

  // 倒计时效果
  useEffect(() => {
    let timer: any
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
    }
    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [countdown])

  // 切换登录方式
  const handleModeSwitch = (mode: LoginMode) => {
    setCurrentMode(mode)
  }

  // 处理登录成功后的跳转
  const handleLoginSuccess = () => {
    if (redirect === 'true' && target) {
      // 有重定向目标，跳转到目标页面
      if (isTabBarPage(target)) {
        Taro.switchTab({ url: target })
      } else {
        Taro.navigateTo({ url: target })
      }
    } else {
      // 默认跳转到我的页面
      Taro.switchTab({ url: '/pages/mine/index' })
    }
  }

  // 表单输入处理
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 手机号验证
  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  // 发送验证码
  const handleSendCode = async () => {
    if (!validatePhone(formData.phone)) {
      Taro.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }

    setCodeLoading(true)
    try {
      const response = await commonApi.getPhoneVerificationCode({
        phone: formData.phone,
        bizCode: 'miniLogin',
        bizType: 'mini'
      })

      if (response.code === STATUS_CODE.SUCCESS) {
        Taro.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
        setCountdown(60) // 60秒倒计时
      } else {
        throw new Error(response.message || '发送失败')
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      Taro.showToast({
        title: error.message || '发送验证码失败',
        icon: 'none'
      })
    } finally {
      setCodeLoading(false)
    }
  }

  // 表单验证
  const validateForm = (): boolean => {
    if (currentMode === 'account') {
      if (!formData.userName.trim()) {
        Taro.showToast({
          title: '请输入用户名',
          icon: 'none'
        })
        return false
      }
      if (!formData.password.trim()) {
        Taro.showToast({
          title: '请输入密码',
          icon: 'none'
        })
        return false
      }
    } else if (currentMode === 'phone') {
      if (!validatePhone(formData.phone)) {
        Taro.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return false
      }
      if (!formData.code.trim()) {
        Taro.showToast({
          title: '请输入验证码',
          icon: 'none'
        })
        return false
      }
    }
    return true
  }

  // 登录处理
  const handleLogin = async () => {
    if (!validateForm()) return

    setLoading(true)
    try {
      let response
      if (currentMode === 'account') {
        response = await commonApi.accountLogin({
          userName: formData.userName.trim(),
          password: aesEcb.encrypt(formData.password.trim())
        })
      } else {
        response = await commonApi.phoneLogin({
          phone: formData.phone.trim(),
          code: formData.code.trim()
        })
      }

      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        // 存储token和用户信息
        Taro.setStorageSync('token', response.data.token)
        Taro.setStorageSync('userInfo', {
          id: response.data.id,
          userName: response.data.userName,
          type: response.data.type
        })

        Taro.showToast({
          title: '登录成功',
          icon: 'success'
        })

        setTimeout(() => {
          handleLoginSuccess()
        }, 1500)
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      Taro.showToast({
        title: error.message || '登录失败，请检查输入信息',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <View className='lawyer-login'>
      {/* 登录标题 - 注册时隐藏 */}
      {currentMode !== 'register' && (
        <View className='login-header'>
          <Text className='title'>登录</Text>
          {/* 注册/登录切换按键 */}
          <View className='header-switch'>
            没有帐号？
            <Text
              className='switch-link'
              onClick={() => handleModeSwitch('register')}
            >
              点此注册
            </Text>
          </View>
        </View>
      )}

      {/* 注册标题 */}
      {currentMode === 'register' && (
        <View className='login-header'>
          <Text className='title'>注册</Text>
          {/* 注册/登录切换按键 */}
          <View className='header-switch'>
            已有帐号？
            <Text
              className='switch-link'
              onClick={() => handleModeSwitch('account')}
            >
              点此登录
            </Text>
          </View>
        </View>
      )}

      {/* 登录表单区域 */}
      {currentMode !== 'register' && (
        <View className='login-form-container'>
          {/* 账号登录表单 */}
          {currentMode === 'account' && (
            <View className='form-section'>
              <View className='form-group'>
                <View className='input-label'>用户名</View>
                <Input
                  className='form-input'
                  placeholder='请输入用户名'
                  value={formData.userName}
                  onInput={(e) => handleInputChange('userName', e.detail.value)}
                  maxlength={50}
                />
              </View>

              <View className='form-group'>
                <View className='input-label'>密码</View>
                <View className='password-input-group'>
                  <Input
                    className='form-input password-input'
                    placeholder='请输入密码'
                    password={!showPassword}
                    value={formData.password}
                    onInput={(e) => handleInputChange('password', e.detail.value)}
                    maxlength={20}
                  />
                  <View
                    className='password-toggle'
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    <Image
                      className='eye-icon'
                      src={showPassword ? eyeCloseIcon : eyeIcon}
                      mode='aspectFit'
                    />
                  </View>
                </View>
              </View>
            </View>
          )}

          {/* 手机验证码登录表单 */}
          {currentMode === 'phone' && (
            <View className='form-section'>
              <View className='form-group'>
                <View className='input-label'>手机号</View>
                <Input
                  className='form-input'
                  placeholder='请输入手机号'
                  type='number'
                  value={formData.phone}
                  onInput={(e) => handleInputChange('phone', e.detail.value)}
                  maxlength={11}
                />
              </View>

              <View className='form-group'>
                <View className='input-label'>验证码</View>
                <View className='code-input-group'>
                  <Input
                    className='form-input code-input'
                    placeholder='请输入验证码'
                    type='number'
                    value={formData.code}
                    onInput={(e) => handleInputChange('code', e.detail.value)}
                    maxlength={6}
                  />
                  <Button
                    className='code-btn'
                    onClick={handleSendCode}
                    loading={codeLoading}
                    disabled={codeLoading || countdown > 0}
                  >
                    {countdown > 0 ? `${countdown}s` : '获取验证码'}
                  </Button>
                </View>
              </View>
            </View>
          )}

          {/* 登录按键和模式切换 */}
          <View className='login-actions'>
            {/* 登录方式切换按键 */}
            <View className='mode-switch'>
              <Text
                className='mode-switch-link'
                onClick={() => handleModeSwitch(currentMode === 'account' ? 'phone' : 'account')}
              >
                {currentMode === 'account' ? '短信验证登录' : '用户名密码登录'}
              </Text>
            </View>
            <Button
              className='login-btn'
              onClick={handleLogin}
              loading={loading}
              disabled={loading}
            >
              {loading ? '登录中...' : '登录'}
            </Button>
          </View>
        </View>
      )}

      {/* 注册表单区域 */}
      {currentMode === 'register' && (
        <View className='login-form-container'>
          <RegisterForm
            onSwitchToLogin={() => handleModeSwitch('account')}
            redirect={redirect}
            target={target}
          />
        </View>
      )}
    </View>
  )
}

export default LawyerLogin
